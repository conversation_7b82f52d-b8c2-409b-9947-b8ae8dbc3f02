/**
 * Bridge Script (Content Script - Isolated World)
 * 
 * This script acts as a bridge between the main world (interceptor) and
 * the extension's background service worker.
 */

let platformModulePath = null;
let debugLoggingEnabled = false;
let interceptorReady = false;

function sendInitMessageToInterceptor() {
    // Only proceed if both the path is available and the interceptor is ready
    if (!platformModulePath || !interceptorReady) {
        return;
    }

    if (debugLoggingEnabled) console.log('LLMLog Bridge: Sending init message to interceptor.');
    
    const fullModuleUrl = chrome.runtime.getURL(platformModulePath);
    const loggerUrl = chrome.runtime.getURL('modules/logger.js');

    // Send the correct message type that the interceptor is expecting
    window.postMessage({ 
        type: 'LLMLOG_INIT', 
        payload: { 
            modulePath: fullModuleUrl,
            loggerPath: loggerUrl,
            debugMode: debugLoggingEnabled 
        } 
    }, '*');
}

// Robust sendMessage wrapper
function sendMessageRobust(message, callback) {
    try {
        const promise = chrome.runtime.sendMessage(message, callback);
        if (promise && typeof promise.catch === 'function') {
            promise.catch(error => {
                if (error.message.includes('Extension context invalidated')) {
                    if (debugLoggingEnabled) console.warn('LLMLog Bridge: Extension context invalidated. Service worker likely restarted. This is expected.');
                } else {
                    console.error('LLMLog Bridge: Error sending message:', error);
                }
            });
        }
    } catch (error) {
        if (error.message.includes('Extension context invalidated')) {
            if (debugLoggingEnabled) console.warn('LLMLog Bridge: Extension context invalidated. Service worker likely restarted. This is expected.');
        } else {
            console.error('LLMLog Bridge: Failed to send message:', error);
        }
    }
}

// Listen for messages from the interceptor (main world)
window.addEventListener('message', (event) => {
    if (event.source !== window) return;

    // Handle conversation data from the interceptor
    if (event.data.type === 'LLMLOG_CONVERSATION') {
        if (debugLoggingEnabled) console.log('LLMLog Bridge: Received conversation from interceptor.', event.data.payload);
        
        sendMessageRobust({
            namespace: 'database',
            action: 'saveConversation',
            payload: event.data.payload
        }, response => {
            if (debugLoggingEnabled) console.log('LLMLog Bridge: Save confirmation received.', response);
        });
    }

    // Handle ready signal from the interceptor
    if (event.data.type === 'LLMLOG_INTERCEPTOR_READY') {
        if (debugLoggingEnabled) console.log('LLMLog Bridge: Interceptor is ready.');
        interceptorReady = true;
        sendInitMessageToInterceptor(); // Attempt to send the init message now
    }
});

// Keep the service worker alive
let serviceWorkerPort = null;

function connectToServiceWorker() {
    serviceWorkerPort = chrome.runtime.connect({ name: 'llmlog-bridge' });
    if (debugLoggingEnabled) console.log('LLMLog Bridge: Connecting to service worker...');

    serviceWorkerPort.onDisconnect.addListener(() => {
        if (debugLoggingEnabled) console.warn('LLMLog Bridge: Service worker port disconnected. Reconnecting...');
        serviceWorkerPort = null; // Clear the old port
        // Add a small delay before reconnecting to avoid spamming
        setTimeout(connectToServiceWorker, 1000);
    });
}


// Determine the platform and fetch the config from the service worker
async function initializeBridge() {
    sendMessageRobust({
        namespace: 'settings',
        action: 'get',
        payload: { key: 'debugLoggingEnabled' }
    }, (enabled) => {
        debugLoggingEnabled = enabled;
    });

    connectToServiceWorker(); // Establish the persistent connection

    let platformName = null;
    if (window.location.hostname.includes('chat.openai') || window.location.hostname.includes('chatgpt')) {
        platformName = 'chatgpt';
    } else if (window.location.hostname.includes('gemini.google')) {
        platformName = 'gemini';
    }

    if (platformName) {
        if (debugLoggingEnabled) console.log(`LLMLog Bridge: Platform detected: ${platformName}. Requesting config.`);
        sendMessageRobust({
            namespace: 'capture',
            action: 'getPlatformConfig',
            payload: { platform: platformName }
        }, (response) => {
            if (chrome.runtime.lastError) {
                console.error('LLMLog Bridge: Error getting platform module path:', chrome.runtime.lastError.message);
                return;
            }
            if (response && response.modulePath) {
                platformModulePath = response.modulePath;
                if (debugLoggingEnabled) console.log('LLMLog Bridge: Platform module path received and stored.', platformModulePath);
                sendInitMessageToInterceptor(); // Attempt to send the init message now
            } else {
                console.error('LLMLog Bridge: Did not receive a valid platform module path.');
            }
        });
    }
}

initializeBridge();
