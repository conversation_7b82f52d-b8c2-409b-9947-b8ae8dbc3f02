# LLMLog 專案優化計畫

## 總結

LLMLog 是一個架構清晰、功能明確的瀏覽器擴充功能。它採用了 Manifest V3 標準，並透過 Service Worker、訊息傳遞和 IndexedDB 實現了核心功能。程式碼模組化程度高，易於理解和維護。

然而，在性能、架構、安全性和使用者體驗方面，仍有進一步的優化空間。本計畫將從以下幾個方面提出具體建議。

---

## 1. 架構與程式碼品質優化

### 1.1. 引入設定管理模組 (DONE)
- **現狀**: `service-worker.js` 和 `router.js` 中直接使用 `chrome.storage.local.get` 來獲取設定（如 `debugLoggingEnabled`）。
- **問題**: 設定項目分散在程式碼各處，未來若增加更多設定，管理會變得困難。
- **建議**: 建立一個 `modules/settings.js` 模組，集中管理所有設定的讀取和寫入。提供如 `getSetting('key')` 和 `setSetting('key', 'value')` 的 API。這能統一管理預設值，並方便未來加入設定變更的監聽。

### 1.2. 抽象化 IndexedDB 操作
- **現狀**: `storage.js` 中直接使用 IndexedDB API，雖然已經封裝，但程式碼仍較為冗長。
- **問題**: IndexedDB 的原生 API 基於事件，使用起來較為繁瑣，容易出錯。
- **建議**: 引入一個輕量級的 IndexedDB 封裝庫（例如 `idb` by Jake Archibald）。這可以將程式碼簡化為更直觀的 `async/await` 語法，提高可讀性和健壯性。

### 1.3. 統一訊息回應格式 (DONE)
- **現狀**: `router.js` 和 `storage.js` 中的 API 回應格式不完全統一。有些直接回傳資料，有些則包裝在 `{ status: 'success', data: ... }` 物件中。
- **問題**: 不一致的回應格式會增加前端處的複雜度。
- **建議**: 制定統一的訊息回應格式，例如：
  - 成功: `{ status: 'success', data: ... }`
  - 失敗: `{ status: 'error', message: '...', details: ... }`
  並在 `router.js` 的 `handleMessage` 中進行統一處理。

---

## 2. 性能優化

### 2.1. 彈出視窗 (Popup) 列表渲染優化
- **現狀**: `popup.js` 中的 `renderConversations` 函式在每次渲染時都會 `innerHTML = ''` 清空整個列表，然後重新建立所有 DOM 元素。
- **問題**: 當對話記錄非常多時，這種方式會導致明顯的 UI 卡頓和閃爍。
- **建議**:
  - **虛擬列表 (Virtual Scrolling)**: 對於大量資料，這是最佳方案。只渲染可視區域內的列表項，大幅提升性能。可以引入輕量級的虛擬滾動庫或自行實現。
  - **DOM Diffing**: 如果不想引入虛擬列表，可以實現一個簡單的 DOM 比對更新機制。為每個列表項設定一個唯一的 `data-id` 屬性，在重新渲染時，比對新舊資料，只新增、刪除或更新有變動的 DOM 元素，而不是全部銷毀重建。

### 2.2. 搜尋功能優化
- **現狀**: 搜尋是在客戶端對所有已載入的對話記錄進行即時篩選。
- **問題**: 當對話數量達到數千甚至數萬筆時，在主執行緒進行大量的字串比對會造成 UI 卡頓。
- **建議**:
  - **Web Worker**: 將搜尋邏輯移至 Web Worker 中執行，避免阻塞主執行緒。
  - **資料庫索引**: 對於更進階的搜尋，可以考慮使用 IndexedDB 的全文搜尋索引（需要第三方庫支援，如 `dexie-full-text-search`）或在儲存時建立自己的關鍵字索引。

### 2.3. Service Worker 啟動優化
- **現狀**: `service-worker.js` 中的 `main` 函式包含了非同步的初始化過程。
- **問題**: Service Worker 的啟動時間對擴充功能的反應速度至關重要。
- **建議**: 盡可能減少啟動時必須執行的非同步操作。例如，`logger` 的初始化可以延遲到第一次需要使用時再進行。

---

## 3. 安全性強化

### 3.1. 內容安全策略 (CSP) 強化
- **現狀**: `manifest.json` 中的 `content_security_policy` 允許了 `connect-src` 到 `https://*.openai.com` 等網域。
- **問題**: 雖然是功能所需，但應確保這是最小權限。
- **建議**: 定期審查 `host_permissions` 和 CSP 規則，確保它們僅包含絕對必要的網域，避免過於寬鬆的設定。

### 3.2. 內容腳本 (Content Script) 安全
- **現狀**: `popup.js` 中使用了 `innerHTML` 來渲染從 `marked.parse()` 回傳的 HTML。
- **問題**: `marked` 預設會過濾掉不安全的 HTML，但依賴庫的安全性設定始終存在風險。如果來配置不當，可能導致 XSS 漏洞。
- **建議**:
  - **使用 `DOMPurify`**: 在將 `marked` 解析後的 HTML 插入到 DOM 之前，先使用 `DOMPurify` 進行一次額外的清理。`DOMPurify.sanitize(marked.parse(content))`。
  - **嚴格的 CSP**: 在 `popup.html` 中可以加入更格的 `<meta http-equiv="Content-Security-Policy" ...>` 標籤，禁止內聯腳本和不信任的資源載入。

---

## 4. 使用者體驗 (UX) 改善

### 4.1. 刪除操作的確認機制
- **現狀**: 刪除操作使用瀏覽器原生的 `confirm()` 對話框。
- **問題**: `confirm()` 會阻塞 UI，且樣式無法自訂，體驗不佳。
- **建議**: 在 `popup.html` 中實作一個自訂的確認對話框或一個「撤銷」刪除的 Toast 通知。這能提供更流暢、非阻塞的操作體驗。

### 4.2. 引入分頁或無限滾動
- **現狀**: 一次性載入並顯示所有對話。
- **問題**: 當資料量大時，會導致初次載入緩慢和 DOM 元素過多。
- **建議**: 在 `storage.js` 中實作分頁邏輯 (`getConversations({ page, limit })`)，並在 `popup.js` 中實現「無限滾動」或「點擊載入更多」的功能。

### 4.3. 增加選項頁面功能
- **現狀**: 選項頁面是 `debug.html`，功能單一。
- **建議**: 建立一個真正的 `options.html` 頁面，提供以下功能：
  - 開關偵錯日誌。
  - 資料匯出/匯入功能。
  - 清空所有資料的按鈕。
  - 設定每個平台是否自動擷取。
