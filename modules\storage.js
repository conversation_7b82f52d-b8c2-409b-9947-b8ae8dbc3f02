/**
 * Storage Module (Repository Pattern)
 * 
 * This module encapsulates all interactions with the IndexedDB,
 * providing a clean, promise-based API for the rest of the application.
 */

const DB_NAME = 'LLMLogDB';
const DB_VERSION = 1;
const STORE_NAME = 'conversations';

let dbPromise = null;

function getDB() {
    if (!dbPromise) {
        dbPromise = new Promise((resolve, reject) => {
            const request = indexedDB.open(DB_NAME, DB_VERSION);
            request.onerror = () => reject("Error opening database");
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains(STORE_NAME)) {
                    const store = db.createObjectStore(STORE_NAME, { keyPath: 'id', autoIncrement: true });
                    store.createIndex('createdAt', 'createdAt', { unique: false });
                }
            };
            request.onsuccess = (event) => resolve(event.target.result);
        });
    }
    return dbPromise;
}

export async function saveConversation(conversationData) {
    try {
        const db = await getDB();
        const readTransaction = db.transaction(STORE_NAME, 'readonly');
        const store = readTransaction.objectStore(STORE_NAME);
        const index = store.index('createdAt');
        const cursorRequest = index.openCursor(null, 'prev');

        const lastRecord = await new Promise((resolve, reject) => {
            cursorRequest.onsuccess = (event) => {
                const cursor = event.target.result;
                resolve(cursor ? cursor.value : null);
            };
            cursorRequest.onerror = (event) => reject(event.target.error);
        });

        if (lastRecord) {
            const timeDifference = new Date(conversationData.createdAt).getTime() - new Date(lastRecord.createdAt).getTime();
            const isDuplicateContent = lastRecord.prompt === conversationData.prompt &&
                lastRecord.response === conversationData.response &&
                lastRecord.platform === conversationData.platform;

            if (isDuplicateContent && timeDifference < 5000) { // 5-second window
                console.log('Duplicate conversation detected, skipping save.');
                return { status: 'success', data: { id: lastRecord.id, duplicate: true } };
            }
        }

        const writeTransaction = db.transaction(STORE_NAME, 'readwrite');
        const writeStore = writeTransaction.objectStore(STORE_NAME);
        const addRequest = writeStore.add(conversationData);

        const newId = await new Promise((resolve, reject) => {
            addRequest.onsuccess = (event) => resolve(event.target.result);
            addRequest.onerror = (event) => reject(event.target.error);
        });

        return { status: 'success', data: { id: newId } };
    } catch (error) {
        return { status: 'error', message: error.message, details: error };
    }
}

export async function getAllConversations() {
    try {
        const db = await getDB();
        const transaction = db.transaction(STORE_NAME, 'readonly');
        const store = transaction.objectStore(STORE_NAME);
        const index = store.index('createdAt');
        const request = index.getAll();

        const result = await new Promise((resolve, reject) => {
            request.onsuccess = (event) => resolve(event.target.result);
            request.onerror = (event) => reject(event.target.error);
        });

        return { status: 'success', data: result.reverse() };
    } catch (error) {
        return { status: 'error', message: error.message, details: error };
    }
}

export async function deleteConversation({ id }) {
    try {
        const db = await getDB();
        const transaction = db.transaction(STORE_NAME, 'readwrite');
        const store = transaction.objectStore(STORE_NAME);
        const request = store.delete(id);

        await new Promise((resolve, reject) => {
            request.onsuccess = () => resolve();
            request.onerror = (event) => reject(event.target.error);
        });

        return { status: 'success', data: { id } };
    } catch (error) {
        return { status: 'error', message: error.message, details: error };
    }
}
